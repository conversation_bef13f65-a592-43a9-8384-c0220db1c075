# Custom Behavior Trees for nav2_test_utils

This directory contains custom behavior tree XML files for the Nav2 navigation stack. These behavior trees define the high-level logic for robot navigation tasks.

## Available Behavior Trees

### 1. `follow_point.xml`
- **Purpose**: Follows a dynamic pose to a certain distance
- **Use Case**: Point following with truncated path planning
- **Key Features**:
  - Uses `TruncatePath` to limit path distance to 1.0 meters
  - Continuous replanning with `<PERSON>Controller` at 1 Hz
  - Suitable for dynamic target following

### 2. `navigate_to_pose_with_replanning.xml`
- **Purpose**: Standard navigation with comprehensive recovery behaviors
- **Use Case**: Robust navigation to a single goal pose
- **Key Features**:
  - Multiple recovery strategies (clearing costmaps, spinning, backing up, waiting)
  - Hierarchical recovery with `RecoveryNode`
  - Suitable for challenging environments

### 3. `navigate_through_poses.xml`
- **Purpose**: Navigate through multiple waypoints sequentially
- **Use Case**: Patrol missions, multi-waypoint navigation
- **Key Features**:
  - Uses `ComputePathThroughPoses` for multi-goal planning
  - Same recovery behaviors as single pose navigation
  - Efficient for waypoint following

## Usage

### Method 1: Using Launch Parameters

Launch with a specific behavior tree:

```bash
# Use the follow_point behavior tree (default)
ros2 launch nav2_test_utils tb3_sim_follow.py

# Use the comprehensive navigation behavior tree
ros2 launch nav2_test_utils tb3_sim_follow.py \
    nav_to_pose_bt:=/path/to/nav2_test_utils/behavior_trees/navigate_to_pose_with_replanning.xml

# Use the multi-waypoint behavior tree
ros2 launch nav2_test_utils tb3_sim_follow.py \
    nav_through_poses_bt:=/path/to/nav2_test_utils/behavior_trees/navigate_through_poses.xml
```

### Method 2: Using the Nav2 Client

Your `nav2_client_util.cpp` shows how to specify behavior trees programmatically:

```cpp
// Use a specific behavior tree file
std::string bt_xml_file = "/path/to/behavior_trees/follow_point.xml";
node->call_server(x, y, bt_xml_file);
```

### Method 3: Runtime Behavior Tree Selection

You can also send the behavior tree XML content directly:

```cpp
// Send behavior tree XML content as string
std::string bt_xml_content = "<root BTCPP_format=\"4\">...</root>";
node->call_server(x, y, bt_xml_content);
```

## Behavior Tree Structure

### Key Components

1. **Root Element**: `<root BTCPP_format="4" main_tree_to_execute="MainTree">`
2. **Main Tree**: `<BehaviorTree ID="MainTree">`
3. **Control Nodes**: 
   - `PipelineSequence`: Executes children in sequence, can be preempted
   - `RecoveryNode`: Executes recovery behaviors on failure
   - `RateController`: Controls execution frequency
4. **Action Nodes**:
   - `ComputePathToPose`: Plans path to single goal
   - `ComputePathThroughPoses`: Plans path through multiple goals
   - `FollowPath`: Executes path following
   - `TruncatePath`: Limits path length
5. **Condition Nodes**:
   - `GoalUpdated`: Checks if goal has been updated

### Blackboard Variables

Behavior trees use blackboard variables for data sharing:
- `{goal}`: Target pose
- `{goals}`: Multiple target poses
- `{path}`: Computed path
- `{truncated_path}`: Path after truncation
- `{selected_controller}`: Active controller plugin
- `{selected_planner}`: Active planner plugin

## Creating Custom Behavior Trees

### Basic Template

```xml
<root BTCPP_format="4" main_tree_to_execute="MainTree">
    <BehaviorTree ID="MainTree">
        <!-- Your behavior tree logic here -->
    </BehaviorTree>
</root>
```

### Best Practices

1. **Naming**: Use descriptive names for your behavior trees
2. **Comments**: Add XML comments explaining the purpose
3. **Recovery**: Include appropriate recovery behaviors
4. **Rate Control**: Use `RateController` for replanning behaviors
5. **Error Handling**: Use `RecoveryNode` for robust error handling
6. **Modularity**: Consider breaking complex trees into subtrees

### Testing Your Behavior Trees

1. **Syntax Validation**: Ensure XML is well-formed
2. **BT.CPP Validation**: Test with BehaviorTree.CPP tools
3. **Simulation Testing**: Test in Gazebo simulation first
4. **Groot Monitoring**: Use Groot for visual debugging

## Troubleshooting

### Common Issues

1. **File Not Found**: Ensure behavior tree file paths are correct
2. **XML Syntax Errors**: Validate XML structure
3. **Missing Plugins**: Ensure all required BT plugins are loaded
4. **Parameter Mismatch**: Check parameter names match exactly

### Debugging Tools

1. **Groot**: Visual behavior tree monitoring and editing
2. **ROS 2 Logs**: Check Nav2 logs for BT execution details
3. **Parameter Inspection**: Use `ros2 param list` and `ros2 param get`

## Integration with Nav2 Stack

The behavior trees integrate with Nav2 through:
- **bt_navigator**: Main behavior tree executor
- **planner_server**: Path planning services
- **controller_server**: Path following services  
- **behavior_server**: Recovery behavior services
- **costmap_2d**: Environmental representation

For more information, see the [Nav2 Behavior Trees documentation](https://navigation.ros.org/behavior_trees/index.html).
