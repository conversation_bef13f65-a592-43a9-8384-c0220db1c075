#!/usr/bin/env python3

"""
Test script for custom behavior trees in nav2_test_utils
This script helps you easily test different behavior tree configurations
"""

import os
import sys
import subprocess
from pathlib import Path

def get_package_path():
    """Get the path to the nav2_test_utils package"""
    try:
        result = subprocess.run(
            ['ros2', 'pkg', 'prefix', 'nav2_test_utils'],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError:
        # Fallback to current directory structure
        current_dir = Path(__file__).parent.parent
        return str(current_dir)

def list_behavior_trees():
    """List available behavior tree files"""
    pkg_path = get_package_path()
    bt_dir = os.path.join(pkg_path, 'behavior_trees')
    
    if not os.path.exists(bt_dir):
        print(f"Behavior trees directory not found: {bt_dir}")
        return []
    
    bt_files = []
    for file in os.listdir(bt_dir):
        if file.endswith('.xml'):
            bt_files.append(file)
    
    return bt_files

def launch_with_behavior_tree(bt_file=None, additional_args=None):
    """Launch the simulation with a specific behavior tree"""
    pkg_path = get_package_path()
    
    # Base launch command
    cmd = ['ros2', 'launch', 'nav2_test_utils', 'tb3_sim_follow.py']
    
    # Add behavior tree parameter if specified
    if bt_file:
        bt_path = os.path.join(pkg_path, 'behavior_trees', bt_file)
        if os.path.exists(bt_path):
            cmd.extend([f'nav_to_pose_bt:={bt_path}'])
            print(f"Using behavior tree: {bt_file}")
        else:
            print(f"Warning: Behavior tree file not found: {bt_path}")
    
    # Add additional arguments
    if additional_args:
        cmd.extend(additional_args)
    
    print(f"Launching: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Launch failed with error: {e}")
    except KeyboardInterrupt:
        print("\nLaunch interrupted by user")

def main():
    """Main function"""
    print("Nav2 Test Utils - Behavior Tree Tester")
    print("=" * 40)
    
    # List available behavior trees
    bt_files = list_behavior_trees()
    
    if not bt_files:
        print("No behavior tree files found!")
        return
    
    print("Available behavior trees:")
    for i, bt_file in enumerate(bt_files, 1):
        print(f"  {i}. {bt_file}")
    
    print(f"  {len(bt_files) + 1}. Use default (from launch file)")
    print("  0. Exit")
    
    # Get user choice
    try:
        choice = input("\nSelect a behavior tree (number): ").strip()
        
        if choice == '0':
            print("Exiting...")
            return
        elif choice == str(len(bt_files) + 1):
            # Use default
            bt_file = None
        else:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(bt_files):
                bt_file = bt_files[choice_idx]
            else:
                print("Invalid choice!")
                return
    except (ValueError, KeyboardInterrupt):
        print("\nExiting...")
        return
    
    # Ask for additional launch arguments
    additional_args = []
    print("\nOptional launch arguments (press Enter to skip):")
    
    # Common options
    options = [
        ("headless", "Run Gazebo in headless mode", "headless:=true"),
        ("slam", "Enable SLAM", "slam:=true"),
        ("rviz", "Disable RViz", "use_rviz:=false"),
    ]
    
    for key, description, arg in options:
        response = input(f"  {description}? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            additional_args.append(arg)
    
    # Custom arguments
    custom_args = input("  Additional arguments (space-separated): ").strip()
    if custom_args:
        additional_args.extend(custom_args.split())
    
    # Launch
    print("\nLaunching simulation...")
    launch_with_behavior_tree(bt_file, additional_args)

if __name__ == "__main__":
    main()
