[0.010s] Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[0.038s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.181s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.217s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.223s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.229s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.241s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.257s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.297s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.298s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.428s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.491s] -- Found rclcpp_action: 16.0.12 (/opt/ros/humble/share/rclcpp_action/cmake)
[0.532s] -- Found nav2_msgs: 1.1.18 (/opt/ros/humble/share/nav2_msgs/cmake)
[0.598s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[0.690s] -- Added test 'copyright' to check source files copyright and LICENSE
[0.702s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.702s] -- Configured cppcheck include dirs: 
[0.702s] -- Configured cppcheck exclude dirs and/or files: 
[0.714s] -- Added test 'cpplint' to check C / C++ code against the Google style
[0.714s] -- Configured cpplint exclude dirs and/or files: 
[0.717s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.721s] -- Added test 'lint_cmake' to check CMake code style
[0.723s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.738s] -- Added test 'uncrustify' to check C / C++ code style
[0.738s] -- Configured uncrustify additional arguments: 
[0.740s] -- Added test 'xmllint' to check XML markup files
[0.741s] -- Configuring done
[0.762s] -- Generating done
[0.773s] -- Build files have been written to: /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[0.802s] [35m[1mConsolidate compiler generated dependencies of target nav2_client_util[0m
[0.803s] [35m[1mConsolidate compiler generated dependencies of target clicked_point_to_pose[0m
[0.827s] [100%] Built target clicked_point_to_pose
[0.827s] [100%] Built target nav2_client_util
[0.844s] Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[0.856s] Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[0.864s] -- Install configuration: ""
[0.865s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util
[0.865s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose
[0.865s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/launch
[0.865s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/launch/tb3_sim_follow.py
[0.865s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/behavior_trees
[0.865s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/behavior_trees/README.md
[0.865s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/behavior_trees/follow_point.xml
[0.866s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/behavior_trees/navigate_to_pose_with_replanning.xml
[0.866s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/behavior_trees/navigate_through_poses.xml
[0.866s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/config
[0.866s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/config/nav2_params.yaml
[0.866s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/test_behavior_trees.py
[0.866s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/package_run_dependencies/nav2_test_utils
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/parent_prefix_path/nav2_test_utils
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.sh
[0.867s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.dsv
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.sh
[0.867s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.dsv
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.bash
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.sh
[0.867s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.zsh
[0.868s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.dsv
[0.868s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv
[0.868s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/packages/nav2_test_utils
[0.868s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig.cmake
[0.868s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig-version.cmake
[0.868s] -- Up-to-date: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.xml
[0.870s] Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
