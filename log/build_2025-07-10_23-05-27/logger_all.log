[0.106s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.107s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x77b00371fa00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x77b00371f5b0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x77b00371f5b0>>)
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.261s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/projects/nav2_test_utils'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.275s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'nav2_test_utils'
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.275s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.296s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.309s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_args' from command line to 'None'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_target' from command line to 'None'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_clean_cache' from command line to 'False'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_clean_first' from command line to 'False'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_force_configure' from command line to 'False'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'ament_cmake_args' from command line to 'None'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'catkin_cmake_args' from command line to 'None'
[0.348s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.348s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_test_utils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils', 'merge_install': False, 'path': '/home/<USER>/projects/nav2_test_utils', 'symlink_install': False, 'test_result_base': None}
[0.349s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.350s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.350s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/projects/nav2_test_utils' with build type 'ament_cmake'
[0.350s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/projects/nav2_test_utils'
[0.352s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.352s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.352s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.361s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[1.195s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[1.207s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[1.220s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_test_utils)
[1.221s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[1.223s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake module files
[1.224s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake config files
[1.224s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_test_utils', 'cmake_prefix_path')
[1.224s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.ps1'
[1.225s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.dsv'
[1.225s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.sh'
[1.227s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib'
[1.227s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[1.227s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/pkgconfig/nav2_test_utils.pc'
[1.227s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/python3.10/site-packages'
[1.228s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[1.228s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.ps1'
[1.229s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv'
[1.230s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.sh'
[1.231s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.bash'
[1.232s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.zsh'
[1.232s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/colcon-core/packages/nav2_test_utils)
[1.233s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_test_utils)
[1.234s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake module files
[1.234s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake config files
[1.234s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_test_utils', 'cmake_prefix_path')
[1.235s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.ps1'
[1.235s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.dsv'
[1.235s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.sh'
[1.236s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib'
[1.236s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[1.236s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/pkgconfig/nav2_test_utils.pc'
[1.237s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/python3.10/site-packages'
[1.237s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[1.237s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.ps1'
[1.238s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv'
[1.238s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.sh'
[1.239s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.bash'
[1.239s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.zsh'
[1.240s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/colcon-core/packages/nav2_test_utils)
[1.240s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1.240s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1.240s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1.241s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1.247s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.247s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1.247s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1.264s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1.264s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.ps1'
[1.266s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/nav2_test_utils/install/_local_setup_util_ps1.py'
[1.268s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.ps1'
[1.269s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.sh'
[1.270s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/nav2_test_utils/install/_local_setup_util_sh.py'
[1.271s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.sh'
[1.273s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.bash'
[1.273s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.bash'
[1.275s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.zsh'
[1.275s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.zsh'
