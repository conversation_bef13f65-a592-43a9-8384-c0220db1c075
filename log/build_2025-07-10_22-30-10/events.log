[0.000000] (-) TimerEvent: {}
[0.000493] (nav2_test_utils) JobQueued: {'identifier': 'nav2_test_utils', 'dependencies': OrderedDict()}
[0.000948] (nav2_test_utils) JobStarted: {'identifier': 'nav2_test_utils'}
[0.010951] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'cmake'}
[0.011370] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/projects/nav2_test_utils', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/projects'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '17849'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/b7e876c8_ec49_49ec_9aa6_2cd77639ee23'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.098130] (nav2_test_utils) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.099487] (-) TimerEvent: {}
[0.167786] (nav2_test_utils) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.176533] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.199610] (-) TimerEvent: {}
[0.231811] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.236913] (nav2_test_utils) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.237232] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.238184] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.241718] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.299718] (-) TimerEvent: {}
[0.301546] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.307096] (nav2_test_utils) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.307290] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.307684] (nav2_test_utils) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.313628] (nav2_test_utils) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.399834] (-) TimerEvent: {}
[0.435605] (nav2_test_utils) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.499929] (-) TimerEvent: {}
[0.516598] (nav2_test_utils) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.562943] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.567433] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.576046] (nav2_test_utils) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.588856] (nav2_test_utils) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.600145] (-) TimerEvent: {}
[0.605203] (nav2_test_utils) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.648845] (nav2_test_utils) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.651582] (nav2_test_utils) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.700378] (-) TimerEvent: {}
[0.723871] (nav2_test_utils) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.746861] (nav2_test_utils) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.777535] (nav2_test_utils) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.786290] (nav2_test_utils) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[0.800429] (-) TimerEvent: {}
[0.849342] (nav2_test_utils) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[0.849653] (nav2_test_utils) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[0.900544] (-) TimerEvent: {}
[0.900752] (nav2_test_utils) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[0.901527] (nav2_test_utils) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[0.951305] (nav2_test_utils) StdoutLine: {'line': b'-- Found rclcpp_action: 16.0.12 (/opt/ros/humble/share/rclcpp_action/cmake)\n'}
[0.986568] (nav2_test_utils) StdoutLine: {'line': b'-- Found nav2_msgs: 1.1.18 (/opt/ros/humble/share/nav2_msgs/cmake)\n'}
[1.000661] (-) TimerEvent: {}
[1.037836] (nav2_test_utils) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.100781] (-) TimerEvent: {}
[1.109826] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.113575] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.113774] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[1.113837] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.117400] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.117621] (nav2_test_utils) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.119196] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.122955] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.123009] (nav2_test_utils) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.123777] (nav2_test_utils) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.124985] (nav2_test_utils) StdoutLine: {'line': b'-- Configuring done\n'}
[1.134624] (nav2_test_utils) StdoutLine: {'line': b'-- Generating done\n'}
[1.138933] (nav2_test_utils) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils\n'}
[1.149491] (nav2_test_utils) CommandEnded: {'returncode': 0}
[1.150002] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'build'}
[1.150535] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/projects'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '17849'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/b7e876c8_ec49_49ec_9aa6_2cd77639ee23'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.192377] (nav2_test_utils) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/nav2_client_util.dir/src/nav2_client_util.cpp.o\x1b[0m\n'}
[1.192598] (nav2_test_utils) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/clicked_point_to_pose.dir/src/clicked_point_to_pose.cpp.o\x1b[0m\n'}
[1.200873] (-) TimerEvent: {}
[1.301162] (-) TimerEvent: {}
[1.401463] (-) TimerEvent: {}
[1.501733] (-) TimerEvent: {}
[1.602014] (-) TimerEvent: {}
[1.702300] (-) TimerEvent: {}
[1.802629] (-) TimerEvent: {}
[1.902893] (-) TimerEvent: {}
[2.003160] (-) TimerEvent: {}
[2.103428] (-) TimerEvent: {}
[2.203767] (-) TimerEvent: {}
[2.304303] (-) TimerEvent: {}
[2.404589] (-) TimerEvent: {}
[2.504876] (-) TimerEvent: {}
[2.605154] (-) TimerEvent: {}
[2.705435] (-) TimerEvent: {}
[2.805737] (-) TimerEvent: {}
[2.906067] (-) TimerEvent: {}
[3.006414] (-) TimerEvent: {}
[3.106676] (-) TimerEvent: {}
[3.206965] (-) TimerEvent: {}
[3.307317] (-) TimerEvent: {}
[3.407655] (-) TimerEvent: {}
[3.508004] (-) TimerEvent: {}
[3.608458] (-) TimerEvent: {}
[3.709038] (-) TimerEvent: {}
[3.809410] (-) TimerEvent: {}
[3.909692] (-) TimerEvent: {}
[4.010018] (-) TimerEvent: {}
[4.110316] (-) TimerEvent: {}
[4.210650] (-) TimerEvent: {}
[4.310966] (-) TimerEvent: {}
[4.411442] (-) TimerEvent: {}
[4.511766] (-) TimerEvent: {}
[4.612056] (-) TimerEvent: {}
[4.712401] (-) TimerEvent: {}
[4.812763] (-) TimerEvent: {}
[4.913194] (-) TimerEvent: {}
[5.013459] (-) TimerEvent: {}
[5.113792] (-) TimerEvent: {}
[5.156047] (nav2_test_utils) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable nav2_client_util\x1b[0m\n'}
[5.213947] (-) TimerEvent: {}
[5.314665] (-) TimerEvent: {}
[5.369974] (nav2_test_utils) StdoutLine: {'line': b'[ 75%] Built target nav2_client_util\n'}
[5.414811] (-) TimerEvent: {}
[5.515372] (-) TimerEvent: {}
[5.615670] (-) TimerEvent: {}
[5.716014] (-) TimerEvent: {}
[5.816298] (-) TimerEvent: {}
[5.916647] (-) TimerEvent: {}
[6.017072] (-) TimerEvent: {}
[6.117448] (-) TimerEvent: {}
[6.217795] (-) TimerEvent: {}
[6.318134] (-) TimerEvent: {}
[6.418436] (-) TimerEvent: {}
[6.518674] (-) TimerEvent: {}
[6.619140] (-) TimerEvent: {}
[6.719418] (-) TimerEvent: {}
[6.819677] (-) TimerEvent: {}
[6.919941] (-) TimerEvent: {}
[7.020280] (-) TimerEvent: {}
[7.120645] (-) TimerEvent: {}
[7.220920] (-) TimerEvent: {}
[7.321180] (-) TimerEvent: {}
[7.421471] (-) TimerEvent: {}
[7.521703] (-) TimerEvent: {}
[7.621966] (-) TimerEvent: {}
[7.722355] (-) TimerEvent: {}
[7.822866] (-) TimerEvent: {}
[7.923175] (-) TimerEvent: {}
[8.023559] (-) TimerEvent: {}
[8.124218] (-) TimerEvent: {}
[8.224598] (-) TimerEvent: {}
[8.324827] (-) TimerEvent: {}
[8.425068] (-) TimerEvent: {}
[8.525319] (-) TimerEvent: {}
[8.625587] (-) TimerEvent: {}
[8.725850] (-) TimerEvent: {}
[8.826130] (-) TimerEvent: {}
[8.836756] (nav2_test_utils) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable clicked_point_to_pose\x1b[0m\n'}
[8.926337] (-) TimerEvent: {}
[9.026708] (-) TimerEvent: {}
[9.127040] (-) TimerEvent: {}
[9.227392] (nav2_test_utils) StdoutLine: {'line': b'[100%] Built target clicked_point_to_pose\n'}
[9.227545] (-) TimerEvent: {}
[9.237422] (nav2_test_utils) CommandEnded: {'returncode': 0}
[9.237905] (nav2_test_utils) JobProgress: {'identifier': 'nav2_test_utils', 'progress': 'install'}
[9.244688] (nav2_test_utils) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'], 'cwd': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'heting'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'x11'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/projects'), ('DESKTOP_SESSION', 'ubuntu-xorg'), ('NVM_BIN', '/home/<USER>/.nvm/versions/node/v22.17.0/bin'), ('NVM_INC', '/home/<USER>/.nvm/versions/node/v22.17.0/include/node'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('SYSTEMD_EXEC_PID', '17849'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('NVM_DIR', '/home/<USER>/.nvm'), ('MANDATORY_PATH', '/usr/share/gconf/ubuntu-xorg.mandatory.path'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'ibus'), ('LOGNAME', 'heting'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('DEFAULTS_PATH', '/usr/share/gconf/ubuntu-xorg.default.path'), ('USERNAME', 'heting'), ('TERM', 'xterm-256color'), ('GAZEBO_MODEL_PATH', ':/opt/ros/humble/share/turtlebot3_gazebo/models'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/home/<USER>/.nvm/versions/node/v22.17.0/bin:/opt/ros/humble/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/heting-laptop:@/tmp/.ICE-unix/17826,unix/heting-laptop:/tmp/.ICE-unix/17826'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/b7e876c8_ec49_49ec_9aa6_2cd77639ee23'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu-xorg'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu-xorg'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils'), ('TURTLEBOT3_MODEL', 'waffle'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu-xorg:/etc/xdg'), ('NVM_CD_FLAGS', ''), ('XDG_DATA_DIRS', '/usr/share/ubuntu-xorg:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[9.250338] (nav2_test_utils) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[9.250484] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util\n'}
[9.250971] (nav2_test_utils) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util" to ""\n'}
[9.251028] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose\n'}
[9.252676] (nav2_test_utils) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose" to ""\n'}
[9.252732] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/package_run_dependencies/nav2_test_utils\n'}
[9.252796] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/parent_prefix_path/nav2_test_utils\n'}
[9.252884] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.sh\n'}
[9.252941] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.dsv\n'}
[9.253026] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.sh\n'}
[9.253089] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.dsv\n'}
[9.253147] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.bash\n'}
[9.253207] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.sh\n'}
[9.253268] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.zsh\n'}
[9.253323] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.dsv\n'}
[9.253385] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv\n'}
[9.253465] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/packages/nav2_test_utils\n'}
[9.253554] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig.cmake\n'}
[9.253647] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig-version.cmake\n'}
[9.253686] (nav2_test_utils) StdoutLine: {'line': b'-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.xml\n'}
[9.254934] (nav2_test_utils) CommandEnded: {'returncode': 0}
[9.275555] (nav2_test_utils) JobEnded: {'identifier': 'nav2_test_utils', 'rc': 0}
[9.275954] (-) EventReactorShutdown: {}
