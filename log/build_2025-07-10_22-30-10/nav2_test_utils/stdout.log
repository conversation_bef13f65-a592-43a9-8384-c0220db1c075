-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclcpp_action: 16.0.12 (/opt/ros/humble/share/rclcpp_action/cmake)
-- Found nav2_msgs: 1.1.18 (/opt/ros/humble/share/nav2_msgs/cmake)
-- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[ 25%] [32mBuilding CXX object CMakeFiles/nav2_client_util.dir/src/nav2_client_util.cpp.o[0m
[ 50%] [32mBuilding CXX object CMakeFiles/clicked_point_to_pose.dir/src/clicked_point_to_pose.cpp.o[0m
[ 75%] [32m[1mLinking CXX executable nav2_client_util[0m
[ 75%] Built target nav2_client_util
[100%] [32m[1mLinking CXX executable clicked_point_to_pose[0m
[100%] Built target clicked_point_to_pose
-- Install configuration: ""
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util
-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util" to ""
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose
-- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose" to ""
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/package_run_dependencies/nav2_test_utils
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/parent_prefix_path/nav2_test_utils
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.sh
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.dsv
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.bash
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.sh
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.zsh
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.dsv
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/packages/nav2_test_utils
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig.cmake
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig-version.cmake
-- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.xml
