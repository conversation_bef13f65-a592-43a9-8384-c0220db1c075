[0.013s] Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/projects/nav2_test_utils -DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils
[0.097s] -- The C compiler identification is GNU 11.4.0
[0.167s] -- The CXX compiler identification is GNU 11.4.0
[0.176s] -- Detecting C compiler ABI info
[0.231s] -- Detecting C compiler ABI info - done
[0.236s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.236s] -- Detecting C compile features
[0.237s] -- Detecting C compile features - done
[0.241s] -- Detecting CXX compiler ABI info
[0.301s] -- Detecting CXX compiler ABI info - done
[0.306s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.306s] -- Detecting CXX compile features
[0.307s] -- Detecting CXX compile features - done
[0.313s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.435s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.516s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.562s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.567s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.575s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.588s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.604s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.648s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.651s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.723s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.746s] -- Found FastRTPS: /opt/ros/humble/include  
[0.777s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.785s] -- Looking for pthread.h
[0.848s] -- Looking for pthread.h - found
[0.849s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[0.900s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[0.901s] -- Found Threads: TRUE  
[0.950s] -- Found rclcpp_action: 16.0.12 (/opt/ros/humble/share/rclcpp_action/cmake)
[0.986s] -- Found nav2_msgs: 1.1.18 (/opt/ros/humble/share/nav2_msgs/cmake)
[1.037s] -- Found ament_lint_auto: 0.12.12 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.109s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.113s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.113s] -- Configured cppcheck include dirs: 
[1.113s] -- Configured cppcheck exclude dirs and/or files: 
[1.117s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.117s] -- Configured cpplint exclude dirs and/or files: 
[1.118s] -- Added test 'lint_cmake' to check CMake code style
[1.122s] -- Added test 'uncrustify' to check C / C++ code style
[1.122s] -- Configured uncrustify additional arguments: 
[1.123s] -- Added test 'xmllint' to check XML markup files
[1.124s] -- Configuring done
[1.134s] -- Generating done
[1.138s] -- Build files have been written to: /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[1.149s] Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/projects/nav2_test_utils -DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils
[1.150s] Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[1.192s] [ 25%] [32mBuilding CXX object CMakeFiles/nav2_client_util.dir/src/nav2_client_util.cpp.o[0m
[1.192s] [ 50%] [32mBuilding CXX object CMakeFiles/clicked_point_to_pose.dir/src/clicked_point_to_pose.cpp.o[0m
[5.155s] [ 75%] [32m[1mLinking CXX executable nav2_client_util[0m
[5.369s] [ 75%] Built target nav2_client_util
[8.836s] [100%] [32m[1mLinking CXX executable clicked_point_to_pose[0m
[9.227s] [100%] Built target clicked_point_to_pose
[9.237s] Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[9.245s] Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[9.249s] -- Install configuration: ""
[9.250s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util
[9.250s] -- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/nav2_client_util" to ""
[9.250s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose
[9.252s] -- Set runtime path of "/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/nav2_test_utils/clicked_point_to_pose" to ""
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/package_run_dependencies/nav2_test_utils
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/parent_prefix_path/nav2_test_utils
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.sh
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/ament_prefix_path.dsv
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.sh
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/environment/path.dsv
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.bash
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.sh
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.zsh
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/local_setup.dsv
[9.252s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv
[9.253s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/ament_index/resource_index/packages/nav2_test_utils
[9.253s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig.cmake
[9.253s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/cmake/nav2_test_utilsConfig-version.cmake
[9.253s] -- Installing: /home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.xml
[9.254s] Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
