[0.124s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.124s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7cf1ff62f670>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7cf1ff62f220>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7cf1ff62f220>>)
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.294s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/projects/nav2_test_utils'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.307s] DEBUG:colcon.colcon_core.package_identification:Package '.' with type 'ros.ament_cmake' and name 'nav2_test_utils'
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.307s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.332s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.333s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.337s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.351s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.382s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_args' from command line to 'None'
[0.382s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_target' from command line to 'None'
[0.382s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.382s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_clean_cache' from command line to 'False'
[0.383s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_clean_first' from command line to 'False'
[0.383s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'cmake_force_configure' from command line to 'False'
[0.383s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'ament_cmake_args' from command line to 'None'
[0.383s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'catkin_cmake_args' from command line to 'None'
[0.383s] Level 5:colcon.colcon_core.verb:set package 'nav2_test_utils' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.383s] DEBUG:colcon.colcon_core.verb:Building package 'nav2_test_utils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils', 'merge_install': False, 'path': '/home/<USER>/projects/nav2_test_utils', 'symlink_install': False, 'test_result_base': None}
[0.383s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.384s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.385s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/projects/nav2_test_utils' with build type 'ament_cmake'
[0.385s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/projects/nav2_test_utils'
[0.390s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.390s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.390s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.398s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/projects/nav2_test_utils -DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils
[1.534s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/projects/nav2_test_utils -DCMAKE_INSTALL_PREFIX=/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils
[1.536s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[9.622s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils -- -j8 -l8
[9.630s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[9.639s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_test_utils)
[9.639s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/projects/nav2_test_utils/build/nav2_test_utils
[9.646s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake module files
[9.647s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake config files
[9.647s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_test_utils', 'cmake_prefix_path')
[9.647s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.ps1'
[9.649s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.dsv'
[9.649s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.sh'
[9.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib'
[9.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[9.650s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/pkgconfig/nav2_test_utils.pc'
[9.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/python3.10/site-packages'
[9.651s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[9.651s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.ps1'
[9.652s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv'
[9.652s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.sh'
[9.653s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.bash'
[9.653s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.zsh'
[9.654s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/colcon-core/packages/nav2_test_utils)
[9.655s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(nav2_test_utils)
[9.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake module files
[9.655s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils' for CMake config files
[9.655s] Level 1:colcon.colcon_core.shell:create_environment_hook('nav2_test_utils', 'cmake_prefix_path')
[9.655s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.ps1'
[9.656s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.dsv'
[9.656s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/hook/cmake_prefix_path.sh'
[9.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib'
[9.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[9.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/pkgconfig/nav2_test_utils.pc'
[9.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/lib/python3.10/site-packages'
[9.657s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/bin'
[9.657s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.ps1'
[9.658s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.dsv'
[9.659s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.sh'
[9.659s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.bash'
[9.659s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/nav2_test_utils/package.zsh'
[9.659s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/projects/nav2_test_utils/install/nav2_test_utils/share/colcon-core/packages/nav2_test_utils)
[9.660s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[9.660s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[9.660s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[9.660s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[9.664s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[9.664s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[9.664s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[9.678s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[9.678s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.ps1'
[9.679s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/nav2_test_utils/install/_local_setup_util_ps1.py'
[9.681s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.ps1'
[9.682s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.sh'
[9.683s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/projects/nav2_test_utils/install/_local_setup_util_sh.py'
[9.683s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.sh'
[9.684s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.bash'
[9.685s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.bash'
[9.687s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/projects/nav2_test_utils/install/local_setup.zsh'
[9.689s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/projects/nav2_test_utils/install/setup.zsh'
