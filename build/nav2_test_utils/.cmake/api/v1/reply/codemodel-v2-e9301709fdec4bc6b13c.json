{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-c3c241a2227c3d6b9b86.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "nav2_test_utils", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "clicked_point_to_pose::@6890427a1f51a3e7e1df", "jsonFile": "target-clicked_point_to_pose-73ca98d4fd4a9ac30f02.json", "name": "clicked_point_to_pose", "projectIndex": 0}, {"directoryIndex": 0, "id": "nav2_client_util::@6890427a1f51a3e7e1df", "jsonFile": "target-nav2_client_util-fb2e652854905b110629.json", "name": "nav2_client_util", "projectIndex": 0}, {"directoryIndex": 0, "id": "nav2_test_utils_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-nav2_test_utils_uninstall-8b490c6239dc246b23f8.json", "name": "nav2_test_utils_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-1653d28555b09d9fcb47.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/projects/nav2_test_utils/build/nav2_test_utils", "source": "/home/<USER>/projects/nav2_test_utils"}, "version": {"major": 2, "minor": 3}}